package main

import (
	"log"
	"net/http"
	"os"
	"strings"

	"line_oauth2/api"
	"line_oauth2/config"
	"line_oauth2/services"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
	"github.com/joho/godotenv"
)

func main() {
	// Load environment variables
	if err := godotenv.Load(); err != nil {
		log.Printf("Warning: .env file not found")
	}

	// Initialize database
	config.InitDB()

	// Initialize services
	lineAuth := services.NewLineAuthService(config.DB)

	// Initialize handlers
	authHandler := api.NewAuthHandler(lineAuth)
	healthHandler := api.NewHealthHandler()

	// Initialize router
	r := chi.NewRouter()

	// Middleware
	r.Use(middleware.Logger)
	r.Use(middleware.Recoverer)
	// Get allowed origins from environment variable or use defaults
	allowedOrigins := []string{"http://localhost:3000", "http://localhost:5173", "http://localhost:5174"}
	if envOrigins := os.Getenv("CORS_ALLOWED_ORIGINS"); envOrigins != "" {
		// Split comma-separated origins
		allowedOrigins = strings.Split(envOrigins, ",")
		// Trim whitespace from each origin
		for i, origin := range allowedOrigins {
			allowedOrigins[i] = strings.TrimSpace(origin)
		}
	}

	r.Use(cors.Handler(cors.Options{
		AllowedOrigins:   allowedOrigins,
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-Requested-With"},
		AllowCredentials: true,
		MaxAge:           300,
	}))

	// Routes
	r.Get("/", func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("Welcome to Line OAuth2 API"))
	})

	// Health check routes
	r.Get("/health", healthHandler.CheckHealth)
	r.Get("/health/ready", healthHandler.CheckReadiness)
	r.Get("/health/live", healthHandler.CheckLiveness)

	// Auth routes
	r.Get("/api/auth/line", authHandler.GetLoginURL)
	r.Get("/api/auth/callback", authHandler.HandleCallback)

	// Start server
	port := os.Getenv("PORT")
	if port == "" {
		port = "8082"
	}

	log.Printf("Server starting on port %s", port)
	if err := http.ListenAndServe(":"+port, r); err != nil {
		log.Fatal(err)
	}
}
