docker build -t rubyhcm/line-oauth2-backend:latest .

docker push rubyhcm/line-oauth2-backend:latest

kubectl apply -f k8s/
--------
kubectl get pods -n line

kubectl get services -n line

kubectl describe service backend-service -n line

kubectl port-forward service/backend-service 30002:80 -n line

minikube service backend-service -n=line

kubectl logs -f deployment/backend-deployment -n line => to view logs realtime
