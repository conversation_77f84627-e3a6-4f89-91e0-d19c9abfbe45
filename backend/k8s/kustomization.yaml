# Kustomization file for Line OAuth2 Backend
# Use with: kubectl apply -k . (instead of kubectl apply -f .)

apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- namespace.yaml
- configmap.yaml
- secret.yaml
- deployment.yaml
- service.yaml
- ingress.yaml
- hpa.yaml
- networkpolicy.yaml

# Use 'labels' instead of deprecated 'commonLabels'
# Only add labels that don't conflict with selectors
labels:
- pairs:
    version: v1.0.0
    managed-by: kustomize
  includeSelectors: false

images:
- name: rubyhcm/line-oauth2-backend
  newTag: latest

# Uncomment to add resource limits across all resources
# patchesStrategicMerge:
# - resource-limits.yaml
