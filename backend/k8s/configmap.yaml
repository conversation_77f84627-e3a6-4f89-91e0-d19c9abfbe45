apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: line
  labels:
    app: line-oauth2-backend
data:
  PORT: "8080"
  DATABASE_URL: "postgres://ps_user:<EMAIL>:5432/ps_db?sslmode=disable"
  # Add your frontend domain(s) here, comma-separated
  # Examples:
  # - For local development: "http://localhost:3000,http://localhost:5173"
  # - For production: "https://your-frontend-domain.com,https://www.your-frontend-domain.com"
  # - For wildcard (less secure): "*"
  CORS_ALLOWED_ORIGINS: "*"
  
