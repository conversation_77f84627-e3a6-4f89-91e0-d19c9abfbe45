apiVersion: v1
kind: Secret
metadata:
  name: backend-secret
  namespace: line
  labels:
    app: line-oauth2-backend
type: Opaque
data:
  # Base64 encoded values - replace with your actual LINE credentials
  # To encode: echo -n "your-value" | base64
  LINE_CHANNEL_ID: MjAwNzU2ODk1Ng==  # Replace with: echo -n "your-channel-id" | base64
  LINE_CHANNEL_SECRET: ZjNmNWYzMzI3NDE3NjRhM2I2MDYzM2U0NWFkNzlhNjYK  # Replace with: echo -n "your-channel-secret" | base64
  LINE_CALLBACK_URL: aHR0cDovL2JhY2tlbmQtc2VydmljZTo4MC9hcGkvYXV0aC9jYWxsYmFjaw==  # Replace with: echo -n "your-callback-url" | base64
